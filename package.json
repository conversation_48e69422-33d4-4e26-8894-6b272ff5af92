{"name": "voicea", "version": "1.0.0", "description": "Voice recording app with global shortcuts and transcription", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "debug": "DEBUG_LOGS=true electron .", "build": "node scripts/build.js", "build:win": "electron-builder --win --publish never", "build:mac": "electron-builder --mac --publish never", "build:linux": "electron-builder --linux --publish never", "build:all": "node scripts/build.js all", "build:current": "node scripts/build.js current", "build:debug": "electron-builder --win --config.extraMetadata.main=main.js", "build:bitbucket": "node scripts/bitbucket-build.js", "build:bitbucket:win": "node scripts/bitbucket-build.js windows", "build:bitbucket:mac": "node scripts/bitbucket-build.js mac", "build:bitbucket:linux": "node scripts/bitbucket-build.js linux", "clean": "rm -rf dist node_modules package-lock.json", "reinstall": "npm run clean && npm install", "prepare-icons": "node scripts/prepare-icons.js", "test": "echo \"No tests specified\" && exit 0", "postinstall": "electron-builder install-app-deps"}, "keywords": ["voice", "recording", "transcription", "openai", "whisper", "electron"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9"}, "dependencies": {"abort-controller": "^3.0.0", "agentkeepalive": "^4.6.0", "auto-launch": "^5.0.6", "dotenv": "^17.2.0", "formdata-node": "^6.0.3", "ms": "^2.1.3", "node-fetch": "^2.7.0", "node-gyp-build": "^4.8.4", "openai": "^4.104.0", "path-is-absolute": "^2.0.0", "tr46": "^5.1.1", "uiohook-napi": "^1.5.4", "undici": "^5.26.5", "web-streams-polyfill": "^4.1.0", "webidl-conversions": "^7.0.0", "whatwg-url": "^14.2.0"}, "build": {"appId": "com.easyvoice.app", "productName": "Easy Voice", "directories": {"output": "dist"}, "publish": null, "extraFiles": ["config.json"], "files": ["**/*", "!node_modules/**/*", "node_modules/openai/**/*", "node_modules/uiohook-napi/**/*", "node_modules/node-gyp-build/**/*", "node_modules/dotenv/**/*", "node_modules/node-fetch/**/*", "node_modules/whatwg-url/**/*", "node_modules/webidl-conversions/**/*", "node_modules/tr46/**/*", "node_modules/formdata-node/**/*", "node_modules/undici/**/*", "node_modules/web-streams-polyfill/**/*", "node_modules/agentkeepalive/**/*", "node_modules/humanize-ms/**/*", "node_modules/ms/**/*", "node_modules/abort-controller/**/*", "node_modules/auto-launch/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.png"}, "mac": {"target": [{"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.png", "category": "public.app-category.productivity", "hardenedRuntime": false, "gatekeeperAssess": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Easy Voice"}}}