<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recording Widget</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            user-select: none;
        }
        
        .widget {
            width: 300px;
            height: 100px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .recording-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .pulse {
            width: 12px;
            height: 12px;
            background: #ff4444;
            border-radius: 50%;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .waveform {
            width: 250px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .waveform-bars {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            gap: 2px;
        }
        
        .bar {
            width: 3px;
            background: #4CAF50;
            border-radius: 2px;
            transition: height 0.1s ease;
        }
        
        .status-text {
            font-size: 12px;
            margin-top: 5px;
            opacity: 0.8;
        }
        
        .hidden {
            display: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-out {
            animation: fadeOut 0.3s ease-out;
        }
        
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="widget" id="widget">
        <div class="recording-indicator">
            <div class="pulse" id="pulse"></div>
            <span>Recording...</span>
        </div>
        
        <div class="waveform">
            <div class="waveform-bars" id="waveformBars">
                <!-- Bars will be generated dynamically -->
            </div>
        </div>
        
        <div class="status-text" id="statusText">Press Ctrl+Alt to start recording</div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        let mediaRecorder;
        let audioChunks = [];
        let audioContext;
        let analyser;
        let dataArray;
        let animationId;
        let isRecording = false;
        
        const widget = document.getElementById('widget');
        const waveformBars = document.getElementById('waveformBars');
        const statusText = document.getElementById('statusText');
        const pulse = document.getElementById('pulse');
        
        // Generate waveform bars
        function generateWaveformBars() {
            waveformBars.innerHTML = '';
            for (let i = 0; i < 50; i++) {
                const bar = document.createElement('div');
                bar.className = 'bar';
                bar.style.height = '2px';
                waveformBars.appendChild(bar);
            }
        }
        
        // Update waveform visualization
        function updateWaveform() {
            if (!analyser || !isRecording) return;
            
            analyser.getByteFrequencyData(dataArray);
            const bars = waveformBars.children;
            
            for (let i = 0; i < bars.length; i++) {
                const value = dataArray[i * 2] || 0;
                const height = Math.max(2, (value / 255) * 40);
                bars[i].style.height = height + 'px';
            }
            
            animationId = requestAnimationFrame(updateWaveform);
        }
        
        // Start recording
        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 44100,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                // Set up audio context for visualization
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const source = audioContext.createMediaStreamSource(stream);
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                source.connect(analyser);
                
                dataArray = new Uint8Array(analyser.frequencyBinCount);
                
                // Set up MediaRecorder
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                audioChunks = [];
                
                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    const reader = new FileReader();
                    
                    reader.onload = async () => {
                        const base64Audio = reader.result;
                        try {
                            await ipcRenderer.invoke('recording-complete', base64Audio);
                        } catch (error) {
                            console.error('Error processing recording:', error);
                        }
                    };
                    
                    reader.readAsDataURL(audioBlob);
                };
                
                mediaRecorder.start();
                isRecording = true;
                
                // Start visualization
                updateWaveform();
                
                // Show widget
                widget.classList.remove('hidden');
                widget.classList.add('fade-in');
                
                statusText.textContent = 'Recording... Press Ctrl+Shift again to stop';
                
            } catch (error) {
                console.error('Error starting recording:', error);
                statusText.textContent = 'Error: Could not access microphone';
            }
        }
        
        // Stop recording
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                
                // Stop visualization
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
                
                // Stop all tracks
                if (mediaRecorder.stream) {
                    mediaRecorder.stream.getTracks().forEach(track => track.stop());
                }
                
                // Close audio context
                if (audioContext) {
                    audioContext.close();
                }
                
                statusText.textContent = 'Transcribing...';
                
                // Hide widget after a short delay
                setTimeout(() => {
                    widget.classList.add('fade-out');
                    setTimeout(() => {
                        widget.classList.add('hidden');
                        widget.classList.remove('fade-out', 'fade-in');
                    }, 300);
                }, 1000);
            }
        }
        
        // Initialize
        generateWaveformBars();
        widget.classList.add('hidden');
        
        // Listen for IPC messages
        ipcRenderer.on('start-recording', () => {
            startRecording();
        });
        
        ipcRenderer.on('stop-recording', () => {
            stopRecording();
        });
        
        // Handle window focus/blur
        window.addEventListener('blur', () => {
            if (isRecording) {
                stopRecording();
            }
        });
    </script>
</body>
</html> 